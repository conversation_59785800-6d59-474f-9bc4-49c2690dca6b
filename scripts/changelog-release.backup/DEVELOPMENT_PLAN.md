# Changelog Release 模块开发计划

## 一、模块概述

Changelog Release 是一个自动化的版本发布管理工具，通过分析 Git 提交历史自动生成插件的版本号和更新日志。模块遵循 KISS (Keep It Simple, Stupid) 和 YAGNI (You Aren't Gonna Need It) 原则，提供简洁、高效的发布流程。

### 开发原则与规范

*   **KISS (Keep It Simple, Stupid)**: 保持代码直接简洁，避免过度复杂化
*   **YAGNI (You Aren't Gonna Need It)**: 只实现当前需要的功能，避免过度工程化
*   **代码复用**: 尽可能复用现有工具函数和模块
*   **中文注释**: 使用 JSDoc 风格的中文注释，为复杂逻辑提供详细说明

## 二、技术栈与依赖

### 外部依赖
- **consola**: 统一的日志输出（复用 @scripts/helpers/logger）
- **execa**: 执行 Git 命令
- **fs-extra**: 文件系统操作
- **semver**: 语义化版本管理
- **yargs**: CLI 命令行参数解析
- **conventional-commits-parser**: 解析 Conventional Commits
- **lodash-es**: 工具函数（deepMerge 等）

### 内部模块复用
- **@scripts/helpers**:
  - `Logger`: 统一日志管理
  - `projectPaths`: 项目路径常量
  - `simpleMatch`: 通配符匹配
  - `deepMerge`: 对象深度合并
  - `ensureDirExists`: 确保目录存在

## 三、模块架构

### 核心模块划分

```
scripts/changelog-release/
├── README.md                 # 模块文档
├── DEVELOPMENT_PLAN.md       # 开发计划（本文档）
├── cli.ts                    # CLI 入口（暂不开发）
├── index.ts                  # 核心流程编排
├── git.ts                    # Git 操作
├── parser.ts                 # 解析和计算
├── generator.ts              # 生成和输出
├── types.ts                  # 共享的类型定义
├── constants.ts              # 共享的常量定义
└── utils.ts                  # 共享的工具函数
```

### 模块职责说明

#### 1. **`types.ts`** - 共享的类型定义
- 定义所有核心数据结构，如 Commit、插件配置、发布信息等。
- 被 `processor.ts` 和外部工具（如 `commitizen-adapter.mjs`）共同使用。

#### 2. **`constants.ts`** - 共享的常量定义
- 定义常量，如 Commit 类型映射、默认配置、文件路径模板等。
- 被 `processor.ts` 和外部工具共同使用。

#### 3. **`git.ts`** - Git 操作
- **职责**: 封装所有 Git 相关的操作。
- **包含**:
  - 获取提交历史（getCommits）
  - 获取标签信息（getTags、getLatestTag）
  - 获取分支信息（getCurrentBranch、getBranchDiff）
  - Git 仓库状态检查（isGitRepo、hasUncommittedChanges）

#### 4. **`parser.ts`** - 解析和计算
- **职责**: 负责解析和计算逻辑。
- **包含**:
  - 解析 Conventional Commits
  - 提取自定义元数据（Applies-To、Issue-ID）
  - 计算语义化版本号（calculateNextVersion）
  - Commit 分类和分组

#### 5. **`generator.ts`** - 生成和输出
- **职责**: 负责所有生成和文件输出操作。
- **包含**:
  - 生成 Changelog Markdown
  - 读写插件配置文件
  - 生成 release-extensions.json
  - 生成 RELEASE.md 发布报告
  - 文件系统操作封装

#### 6. **`index.ts`** - 核心流程编排 (Orchestrator)
- **职责**: 整合各功能模块，实现 `preview` 和 `release` 的核心工作流。
- 导入 `git.ts`、`parser.ts`、`generator.ts` 中的函数，按顺序调用它们来完成完整的发布流程。
- 仅导出必要的函数和类型，作为模块的公共接口。

#### 7. **`cli.ts`** - CLI 入口
- **职责**: 解析命令行参数，并调用 `index.ts` 中导出的相应函数。
- 作为用户与 `changelog-release` 模块交互的入口。

#### 8. **`utils.ts`** - 共享的工具函数
- **职责**: 提供辅助功能，例如文件路径处理、通用数据转换等。
- 被模块内部其他文件（如 `git.ts`, `parser.ts`, `generator.ts`, `index.ts`）以及外部工具（如 `commitizen-adapter.mjs`）共同使用。

**注意**：项目根目录下的 `@commitizen-adapter.mjs` 和 `@commitlint.config.mjs` 也属于 `scripts/changelog-release/` 模块的一部分，它们依赖于此模块的类型和常量定义。

## 四、数据流设计

### Preview 模式流程
```
1. 获取最新标签 → 2. 获取提交历史 → 3. 解析 Commits
→ 4. 分组到插件 → 5. 计算版本号 → 6. 生成 Changelog
→ 7. 更新 next 字段 → 8. 保存配置文件
```

### Release 模式流程
```
1-6. 同 Preview 模式
→ 7. 更新正式字段 → 8. 清空 next 字段
→ 9. 生成 release-extensions.json → 10. 生成 RELEASE.md
→ 11. 保存所有文件
```

## 五、文件路径约定

### 输入文件
- Git 仓库：`.git/`
- 插件管理看板配置：`packages/extensions/extension_dashboard/extensions/{plugin_name}.json`
- 构建配置文件：`packages/extensions/{plugin_name}/extension.config.ts`（用于版本同步）

### 输出文件
- 插件管理看板配置：同输入路径（就地更新）
- 构建配置文件：同步更新版本号
- 发布配置：`release-extensions.json`（项目根目录）
- 发布报告：`.output/{extensionName}/{version}/RELEASE.md`

## 六、接口设计

### 主要导出函数

```typescript
// 生成预览
export async function generatePreview(options: PreviewOptions): Promise<PreviewResult>

// 生成发布
export async function generateRelease(options: ReleaseOptions): Promise<ReleaseResult>

// 获取状态
export async function getStatus(options: StatusOptions): Promise<StatusResult>
```

### 配置选项

```typescript
interface BaseOptions {
  baseBranch?: string      // 基准分支，默认 'main'
  verbose?: boolean         // 详细日志
  extensions?: string[]     // 指定插件列表
}

interface PreviewOptions extends BaseOptions {
  // Preview 特定选项
}

interface ReleaseOptions extends BaseOptions {
  dryRun?: boolean         // 模拟运行
}
```

## 七、错误处理策略

1. **Git 操作失败**：提供清晰的错误信息和解决建议
2. **Commit 格式错误**：跳过无效 Commit，记录警告
3. **文件读写失败**：检查权限，提供详细路径信息
4. **版本冲突**：检测并报告版本号冲突

## 八、测试策略

1. **单元测试**：每个模块独立测试
2. **集成测试**：完整流程测试
3. **模拟数据**：使用固定的 Git 历史进行测试


## 九、注意事项

1. **保持简单**：避免过度设计，只实现必要功能
2. **类型安全**：充分利用 TypeScript 类型系统
3. **错误友好**：提供清晰、可操作的错误信息
4. **日志规范**：使用统一的日志格式和级别
5. **代码复用**：优先使用已有的工具函数

## 十、开发进度

### 阶段一：基础架构搭建（已完成）
- [x] 制定模块架构设计
- [x] 编写 README.md 技术文档
- [x] 创建 DEVELOPMENT_PLAN.md 开发计划

### 阶段二：核心功能实现（已完成）
- [x] 实现 `types.ts` - 类型定义
- [x] 实现 `constants.ts` - 常量定义
- [x] 实现 `git.ts` - Git 操作封装
- [x] 实现 `parser.ts` - Commit 解析和版本计算
- [x] 实现 `generator.ts` - Changelog 生成和文件输出
- [x] 实现 `utils.ts` - 工具函数

### 阶段三：流程编排（已完成）
- [x] 实现 `index.ts` - 核心流程编排
- [x] 实现 preview 模式流程
- [x] 实现 release 模式流程
- [x] 实现 status 命令

### 阶段四：CLI 接口（待开始）
- [ ] 实现 `cli.ts` - 命令行接口
- [ ] 集成 yargs 参数解析
- [ ] 添加交互式提示功能

### 阶段五：测试与优化（待开始）
- [ ] 编写单元测试
- [ ] 编写集成测试
- [ ] 性能优化
- [ ] 文档完善

### 阶段六：集成配置（待开始）
- [ ] 更新 `commitizen-adapter.mjs`
- [ ] 更新 `commitlint.config.mjs`
- [ ] 配置 package.json scripts
- [ ] 编写使用示例
