# Changelog & Release 模块技术文档

## 一、背景与目标

在为多个浏览器扩展（Extensions）进行发布时，手动管理版本号、编写更新日志（Changelog）和准备发布材料的过程不仅耗时，而且极易出错。为提升发布效率与准确性，我们需要一个自动化、可控且安全的流程来取代现有的人工操作。

本模块的核心目标是 **从 Git 提交历史中自动生成扩展插件的发布元数据**，并通过一个分阶段的工作流确保发布过程的安全性与可追溯性。

## 二、设计原则

1.  **约定优于配置**：严格遵循 [Conventional Commits](https://www.conventionalcommits.org/) 规范，并扩展元数据字段，实现自动化解析。
2.  **流程安全**：将文件生成（`preview` 模式）与最终发布（`release` 模式）明确分离，在执行永久性变更前为开发者提供充分的验证机会。
3.  **自动化驱动**：根据 Git 提交历史自动计算语义化版本号、生成结构化更新日志。
4.  **高可维护性**：模块化设计，核心逻辑（Git 操作、Commit 解析、版本计算、日志生成、文件读写）高度解耦，职责清晰。

## 三、核心功能

1.  **CLI 命令行工具**：提供 `preview` 和 `release` 两种模式，引导开发者完成安全、分阶段的发布流程。
2.  **Commit 解析**：自动解析符合规范的 Git Commit，提取 `type`, `scope`, `subject` 以及自定义的 `Applies-To`, `Issue-ID` 等元数据。
3.  **语义化版本计算**：根据 Commit 类型（`feat`, `fix`, `refactor`）和是否包含 `BREAKING CHANGE`，自动计算下一个 `major`, `minor`, 或 `patch` 版本。
4.  **Changelog 自动生成**：为每个受影响的插件生成格式化、分组的 Markdown 更新日志。
5.  **发布产物生成**：
    - **预览模式**：更新插件数据文件中的 `next` 字段，用于构建和验证。
    - **发布模式**：更新插件数据文件的 `version`, `changelog` 等字段，同步更新构建配置文件的版本号，并生成用于驱动构建流程的 `release-extensions.json` 和人类可读的 `.output/{extensionName}/{version}/RELEASE.md` 报告。
6.  **版本同步机制**：确保插件数据文件和构建配置文件的版本号保持一致。
7.  **手动关联 Commit**：支持在交互式流程中，将未明确指定 `Applies-To` 字段的 Commit 手动关联到相应的扩展插件。

## 四、文件说明

本模块涉及两种不同用途的文件，理解它们的区别对于正确使用模块至关重要：

### 4.1 插件数据文件
**路径**：`packages/extensions/extension_dashboard/extensions/{plugin_name}.json`

**用途**：存储插件的基本信息和发布历史，供插件管理看板（Dashboard）展示使用。

**特点**：
- 数据存储文件，非配置文件
- 数组格式：`[{ name, version, displayName, changelog, next, ... }]`
- 包含插件的显示信息（displayName、icon）
- 存储发布历史和 changelog
- 包含 `next` 字段用于预览下一版本
- 由 `changelog-release` 模块更新内容

### 4.2 构建配置文件
**路径**：`packages/extensions/{plugin_name}/extension.config.ts`

**用途**：定义插件的构建配置，包含 variants、manifest、i18n 等完整构建信息。

**特点**：
- TypeScript 配置文件
- 包含多个 variants（渠道包）定义
- 定义 manifest.json 的生成规则
- 包含国际化配置和版本号
- 由 `extension-config` 模块管理和处理

### 4.3 版本同步机制
为确保数据一致性，本模块在更新插件数据文件时，会自动同步更新构建配置文件中的版本号，保持两个文件的版本信息一致。

## 五、目录结构与职责

| 文件/目录 | 说明 |
| :--- | :--- |
| `types.ts` | 定义共享的核心数据结构（如 Commit、配置），被多方依赖。 |
| `constants.ts` | 定义共享的常量（如 Commit 类型映射），被多方依赖。 |
| `git.ts` | **Git 操作**：封装所有 Git 相关操作（获取 commits、tags、分支信息等）。 |
| `parser.ts` | **解析和计算**：负责 Commit 解析、元数据提取（Applies-To, Issue-ID）、语义化版本计算。 |
| `generator.ts` | **生成和输出**：负责 Changelog 生成、文件读写（插件配置、release.json、RELEASE.md）。 |
| `index.ts` | **核心流程编排 (Orchestrator)**：导入上述功能模块，编排实现 `preview` 和 `release` 工作流。仅导出必要的函数和类型，作为模块的公共接口。 |
| `cli.ts` | **CLI 入口**：解析命令行参数，调用 `index.ts` 中的函数，作为用户交互的入口。**（暂不开发）** |
| `utils.ts` | 共享的工具函数，提供辅助功能。 |
| `README.md` | 模块技术文档。 |

**注意**：项目根目录下的 `@commitizen-adapter.mjs` 和 `@commitlint.config.mjs` 也属于 `scripts/changelog-release/` 模块的一部分，它们依赖于此模块的类型和常量定义。

## 六、核心流程说明

整个发布流程被设计为三个阶段，确保每一步都清晰可控。

1.  **阶段一：代码提交**
    - 开发者按照 **Commit Message 规范** 提交代码。每个 Commit 必须通过 `Applies-To` 字段指明其影响的一个或多个插件。

2.  **阶段二：生成预览并验证**
    - 开发者在本地运行 `pnpm changelog-release preview`。
    - 脚本会：
        1.  查找自上次发布（最新的 Git Tag）以来的所有相关 Commit。
        2.  解析 Commit，并按插件分组。
        3.  为每个插件计算下一个版本号，并生成 Changelog。
        4.  将结果写入对应插件数据文件的 `next` 字段中。
    - 开发者使用 `next` 中的信息进行本地构建和功能验证。

3.  **阶段三：生成正式发布文件**
    - 验证通过后，开发者运行 `pnpm changelog-release release`。
    - 脚本会：
        1.  执行与 `preview` 相同的分析流程。
        2.  将版本号和 Changelog 更新到插件数据文件的根字段（`version`, `changelog`），并清空 `next` 字段，同时同步更新构建配置文件的版本号。
        3.  在项目根目录生成 `release-extensions.json`，用于驱动 `scripts/build` 构建流程。
        4.  在 `.output/{extensionName}/{version}/RELEASE.md` 目录生成 `RELEASE.md`，作为本次发布的聚合报告。
    - **注意**：此脚本 **不会** 自动创建 Git Commit 或 Tag。开发者需要手动将变更的文件（插件数据文件、构建配置、`release-extensions.json`）提交到代码库，并手动打上版本标签。

## 七、主要接口说明 (CLI)

### `changelog-release preview`
> 生成预览版本的 Changelog 和版本号，用于构建和验证。

-   **核心产物**：更新插件数据文件 `packages/extensions/extension_dashboard/extensions/{plugin_name}.json` 中的 `next: { version, changelog }` 字段。
-   **常用选项**：
    -   `--base-branch <branch>`: 指定用于对比的基准分支，默认为 `main`。
    -   `--verbose`: 显示详细的执行日志。

### `changelog-release release`
> 生成正式发布的配置文件和发布报告。

-   **核心产物**：
    1.  更新插件数据文件的根字段 (`version`, `changelog`)。
    2.  创建或更新根目录的 `release-extensions.json`。
    3.  创建或更新 `.output/{extensionName}/{version}/RELEASE.md`。
-   **常用选项**：
    -   `--dry-run`: 试运行模式，模拟所有操作但不会真实写入任何文件。

### `changelog-release status`
> 辅助命令，用于快速查看当前 Git 仓库的分支和工作区状态。

## 八、Commit Message 规范

我们遵循 **Conventional Commits** 标准，并利用 `footer` 承载自定义元数据。

#### 模板
```git
<type>(<scope>): <subject>
<BLANK LINE>
[optional body]
<BLANK LINE>
Applies-To: <plugin-name-1>, <plugin-name-2>
Issue-ID: <issue-id-1>, <issue-id-2>
```

-   **`Applies-To` (可选)**: 指明此 Commit 应用于哪些插件，多个用逗号 `,` 分隔。如果留空，该 Commit 默认不属于任何插件，但可以在生成 Changelog 时手动或通过规则进行派发。
-   **`Issue-ID` (可选)**: 关联的一个或多个内部 issue 编号，多个用逗号 `,` 分隔。格式为 `[sub-task-id#]YYYYMMDD-NN`，其中 `#` 前的子需求 ID 是可选的。

#### 示例

**场景一：常规 Commit**
```git
feat(cookie): 支持导出 Cookie 为 JSON 文件

新增一个导出按钮，允许用户将当前选中的 Cookie 保存为标准的 JSON 格式，方便备份和在不同浏览器间迁移。

Applies-To: cookies_manager
Issue-ID: 3#20250711-03, 20250711-04
```

**场景二：未指定 `Applies-To` 的 Commit**
```git
docs: 更新项目贡献指南

完善了关于代码风格和提交流程的说明。
```
(此 Commit 后续可通过交互式工具或配置文件派发给 `extension_dashboard`, `price_tracker` 等插件)

## 九、常见问题 FAQ

**Q: 如果一个 Commit 忘记写 `Applies-To` 会怎样？**
A: 默认情况下，该 Commit 不会关联到任何插件。但本模块支持在生成 Changelog 的环节，通过交互式提示或预设的配置文件，将这类 Commit 手动关联到一个或多个插件，确保所有相关变更都能被正确追踪。

**Q: 脚本如何确定从哪个 Commit 开始计算？**
A: 默认情况下，它会从当前分支上最新的一个 Git Tag 开始查找。如果没有 Tag，则会处理从 `base-branch` (`main`) 分叉出来的所有提交。

**Q: 脚本会自动帮我提交代码和打 Tag 吗？**
A: 不会。这是一个明确的安全设计。所有 Git 的写操作（`commit`, `tag`）都必须由开发者手动完成，以确保最终发布的版本是经过确认的。

**Q: `extension_dashboard/extensions/{name}.json` 和 `{name}/extension.config.ts` 有什么区别？**
A: 这是两个不同用途的文件：
- `extension_dashboard/extensions/{name}.json`：插件管理看板的数据文件，存储插件基本信息、版本历史和 changelog，由本模块更新内容
- `{name}/extension.config.ts`：构建配置文件，定义插件的 variants、manifest、i18n 等构建信息，由 `extension-config` 模块管理
本模块会自动同步两个文件中的版本号，确保数据一致性。

## 十、未来扩展

1. **钩子系统**：支持自定义前后处理逻辑

## 十一、维护与扩展约定

1.  所有类型定义和常量集中于 `types.ts` 和 `constants.ts`，它们是多方依赖的共享配置。
2.  功能实现按领域拆分：
    - Git 相关操作封装在 `git.ts`
    - 解析和计算逻辑封装在 `parser.ts`
    - 生成和输出逻辑封装在 `generator.ts`
3.  核心业务流程在 `index.ts` 中编排，保持高层逻辑的清晰性。
4.  Commit Message 规范是整个自动化流程的基石，必须严格遵守。
