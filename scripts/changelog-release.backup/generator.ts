/**
 * Changelog 生成和文件输出
 * 
 * 负责生成 Changelog Markdown、读写插件配置文件、生成发布文件
 */

import { ensureDir, pathExists, readFile, writeFile } from 'fs-extra';
import { dirname } from 'node:path';
import { Logger } from '../helpers/logger';
import { loadExtensionConfigs } from '../extension-config/utils';
import { 
  DEFAULT_CONFIG, 
  FILE_PATHS, 
  CHANGELOG_TEMPLATES, 
  TYPE_EMOJIS,
  COMMIT_TYPES,
  ERROR_MESSAGES 
} from './constants';
import type {
  ExtensionConfig,
  ParsedCommit,
  ChangelogEntry,
  ChangeItem,
  ReleaseInfo,
  ReleaseExtensionsConfig
} from './types';

const logger = Logger.create('Generator');

/**
 * 读取扩展配置文件
 *
 * 读取插件管理看板的配置文件，如果不存在则从 extension.config.ts 获取基本信息创建默认配置
 */
export async function readExtensionConfig(extensionName: string): Promise<ExtensionConfig | null> {
  try {
    // 读取 dashboard 配置文件
    const dashboardConfigPath = FILE_PATHS.extensionConfig.replace('{name}', extensionName);

    if (await pathExists(dashboardConfigPath)) {
      const fileContent = await readFile(dashboardConfigPath, 'utf-8');
      const dashboardData = JSON.parse(fileContent);

      // dashboard 配置文件是数组格式，取第一个元素
      if (Array.isArray(dashboardData) && dashboardData.length > 0) {
        return dashboardData[0] as ExtensionConfig;
      }
    }

    // 如果 dashboard 配置不存在，从 extension.config.ts 获取基本信息
    logger.info(`Dashboard 配置不存在，从 extension.config.ts 获取基本信息: ${extensionName}`);

    const configs = await loadExtensionConfigs([extensionName]);
    const variantConfigs = configs[extensionName];

    if (!variantConfigs || Object.keys(variantConfigs).length === 0) {
      logger.error(`没有找到扩展配置: ${extensionName}`);
      return null;
    }

    // 从第一个 variant 中提取基本信息，创建默认的 dashboard 配置
    const firstVariant = Object.values(variantConfigs)[0];

    const defaultConfig: ExtensionConfig = {
      name: firstVariant.name,
      version: firstVariant.version,
      displayName: firstVariant.name, // 可以后续手动更新
      icon: '',
      changelog: [],
      releaseAt: '',
      releaseDate: ''
    };

    return defaultConfig;
  } catch (error) {
    logger.error(`读取扩展配置失败: ${extensionName}`, error);
    return null;
  }
}

/**
 * 写入扩展配置文件
 *
 * 将配置写入 dashboard 配置文件，保持数组格式
 */
export async function writeExtensionConfig(
  extensionName: string,
  config: ExtensionConfig
): Promise<void> {
  try {
    const configPath = FILE_PATHS.extensionConfig.replace('{name}', extensionName);

    // 确保目录存在
    await ensureDir(dirname(configPath));

    // dashboard 配置文件是数组格式
    const configArray = [config];

    // 格式化 JSON
    const content = JSON.stringify(configArray, null, DEFAULT_CONFIG.jsonIndent);
    await writeFile(configPath, content + '\n', DEFAULT_CONFIG.encoding);

    logger.success(`更新扩展配置: ${extensionName}`);
  } catch (error) {
    logger.error(`写入扩展配置失败: ${extensionName}`, error);
    throw new Error(ERROR_MESSAGES.writeError);
  }
}

/**
 * 生成 Changelog Entry
 */
export function generateChangelogEntry(
  version: string,
  commits: ParsedCommit[],
  date: Date = new Date()
): ChangelogEntry {
  // 按类型分组
  const typeGroups: Record<string, ParsedCommit[]> = {};
  
  for (const commit of commits) {
    const type = commit.type;
    if (!typeGroups[type]) {
      typeGroups[type] = [];
    }
    typeGroups[type].push(commit);
  }
  
  // 生成变更项
  const changes: ChangeItem[] = [];
  
  // 按固定顺序处理类型
  const orderedTypes = ['feat', 'fix', 'perf', 'refactor', 'revert'];
  
  for (const type of orderedTypes) {
    const commits = typeGroups[type];
    if (!commits || commits.length === 0) continue;
    
    for (const commit of commits) {
      changes.push({
        type,
        scope: commit.scope,
        description: commit.subject,
        breaking: commit.breaking,
        commits: [commit.shortHash],
        issues: commit.issueIds.length > 0 ? commit.issueIds : undefined,
      });
    }
  }
  
  return {
    version,
    date: formatDate(date),
    changes,
  };
}

/**
 * 生成版本标题
 */
function formatVersionTitle(version: string, date: string): string {
  return CHANGELOG_TEMPLATES.versionTitle
    .replace('{version}', version)
    .replace('{date}', date);
}

/**
 * 生成类型标题
 */
function formatTypeTitle(type: string): string {
  const typeConfig = COMMIT_TYPES[type as keyof typeof COMMIT_TYPES];
  const emoji = TYPE_EMOJIS[type as keyof typeof TYPE_EMOJIS] || '';
  const title = typeConfig?.description || type;
  
  return CHANGELOG_TEMPLATES.typeTitle
    .replace('{emoji}', emoji)
    .replace('{title}', title);
}

/**
 * 格式化变更项描述
 */
function formatChangeDescription(change: ChangeItem): string {
  return change.scope 
    ? `**${change.scope}**: ${change.description}`
    : change.description;
}

/**
 * 格式化变更项
 */
function formatChangeItem(change: ChangeItem): string {
  const description = formatChangeDescription(change);
  const commits = change.commits.map(c => `[${c}]`).join(', ');
  const issues = change.issues 
    ? ` - ${change.issues.map(i => `#${i}`).join(', ')}`
    : '';
  
  return CHANGELOG_TEMPLATES.changeItem
    .replace('{description}', description)
    .replace('{commits}', commits)
    .replace('{issues}', issues);
}

/**
 * 生成破坏性变更部分
 */
function generateBreakingChangesSection(breakingChanges: ChangeItem[]): string[] {
  if (breakingChanges.length === 0) {
    return [];
  }
  
  const lines: string[] = [
    CHANGELOG_TEMPLATES.breakingTitle,
    ''
  ];
  
  for (const change of breakingChanges) {
    const description = formatChangeDescription(change);
    lines.push(
      CHANGELOG_TEMPLATES.breakingItem.replace('{description}', description)
    );
  }
  lines.push('');
  
  return lines;
}

/**
 * 按类型分组变更
 */
function groupChangesByType(changes: ChangeItem[]): {
  typeGroups: Record<string, ChangeItem[]>;
  breakingChanges: ChangeItem[];
} {
  const typeGroups: Record<string, ChangeItem[]> = {};
  const breakingChanges: ChangeItem[] = [];
  
  for (const change of changes) {
    if (change.breaking) {
      breakingChanges.push(change);
    }
    
    if (!typeGroups[change.type]) {
      typeGroups[change.type] = [];
    }
    typeGroups[change.type].push(change);
  }
  
  return { typeGroups, breakingChanges };
}

/**
 * 生成 Changelog Markdown
 */
export function generateChangelogMarkdown(changelog: ChangelogEntry[]): string {
  const lines: string[] = [];
  
  for (const entry of changelog) {
    // 版本标题
    lines.push(formatVersionTitle(entry.version, entry.date));
    lines.push('');
    
    // 分组变更
    const { typeGroups, breakingChanges } = groupChangesByType(entry.changes);
    
    // 破坏性变更
    lines.push(...generateBreakingChangesSection(breakingChanges));
    
    // 按类型输出变更
    const orderedTypes = ['feat', 'fix', 'perf', 'refactor', 'revert'];
    
    for (const type of orderedTypes) {
      const changes = typeGroups[type];
      if (!changes || changes.length === 0) continue;
      
      lines.push(formatTypeTitle(type));
      lines.push('');
      
      for (const change of changes) {
        lines.push(formatChangeItem(change));
      }
      lines.push('');
    }
  }
  
  return lines.join('\n').trim();
}

/**
 * 更新扩展配置的 next 字段（预览模式）
 */
export async function updateExtensionNext(
  extensionName: string,
  releaseInfo: ReleaseInfo
): Promise<void> {
  const config = await readExtensionConfig(extensionName);
  if (!config) {
    throw new Error(`扩展配置不存在: ${extensionName}`);
  }
  
  // 更新 next 字段
  config.next = {
    version: releaseInfo.newVersion,
    changelog: [releaseInfo.changelog],
  };
  
  await writeExtensionConfig(extensionName, config);
}

/**
 * 更新扩展配置的正式字段（发布模式）
 */
export async function updateExtensionRelease(
  extensionName: string,
  releaseInfo: ReleaseInfo
): Promise<void> {
  const config = await readExtensionConfig(extensionName);
  if (!config) {
    throw new Error(`扩展配置不存在: ${extensionName}`);
  }

  // 更新版本号
  config.version = releaseInfo.newVersion;

  // 更新 changelog
  if (!config.changelog || typeof config.changelog === 'string') {
    config.changelog = [];
  }

  // 确保 changelog 是数组类型
  const changelogArray = Array.isArray(config.changelog) ? config.changelog : [];

  // 添加新的 changelog 条目到最前面
  changelogArray.unshift(releaseInfo.changelog);

  // 限制 changelog 长度
  if (changelogArray.length > 10) {
    config.changelog = changelogArray.slice(0, 10);
  } else {
    config.changelog = changelogArray;
  }

  // 清空 next 字段
  delete config.next;

  // 更新 dashboard 配置文件
  await writeExtensionConfig(extensionName, config);

  // 同步更新 extension.config.ts 中的版本号
  await syncExtensionConfigVersion(extensionName, releaseInfo.newVersion);
}

/**
 * 生成 release-extensions.json
 */
export async function generateReleaseConfig(
  releases: Record<string, ReleaseInfo>
): Promise<void> {
  const extensions = Object.entries(releases).map(([name, info]) => ({
    name,
    version: info.newVersion,
    build: true,
    // 可以根据需要添加 platforms 等字段
  }));
  
  const config: ReleaseExtensionsConfig = {
    generatedAt: new Date().toISOString(),
    extensions,
  };
  
  const content = JSON.stringify(config, null, DEFAULT_CONFIG.jsonIndent);
  await writeFile(FILE_PATHS.releaseConfig, content + '\n');
  
  logger.success(`生成发布配置: ${FILE_PATHS.releaseConfig}`);
}

/**
 * 生成发布报告
 */
export async function generateReleaseReport(
  extensionName: string,
  releaseInfo: ReleaseInfo
): Promise<string> {
  const lines: string[] = [
    `# ${extensionName} Release Report`,
    '',
    `## Version: ${releaseInfo.newVersion}`,
    `**Date**: ${formatDate(new Date())}`,
    `**Previous Version**: ${releaseInfo.currentVersion}`,
    `**Version Bump**: ${releaseInfo.bump}`,
    '',
    '## Changelog',
    '',
    generateChangelogMarkdown([releaseInfo.changelog]),
    '',
    '## Commits',
    '',
  ];
  
  // 添加提交列表
  for (const commit of releaseInfo.commits) {
    lines.push(`- ${commit.shortHash} - ${commit.type}: ${commit.subject}`);
    if (commit.breaking) {
      lines.push(`  - ⚠️ BREAKING CHANGE`);
    }
    if (commit.issueIds.length > 0) {
      lines.push(`  - Issues: ${commit.issueIds.join(', ')}`);
    }
  }
  
  const content = lines.join('\n');
  
  // 写入文件
  const reportPath = FILE_PATHS.releaseReport
    .replace('{extensionName}', extensionName)
    .replace('{version}', releaseInfo.newVersion);
  
  await ensureDir(dirname(reportPath));
  await writeFile(reportPath, content);
  
  logger.success(`生成发布报告: ${reportPath}`);
  
  return reportPath;
}

/**
 * 批量读取扩展配置
 */
export async function readExtensionConfigs(
  extensionNames: string[]
): Promise<Record<string, ExtensionConfig>> {
  const configs: Record<string, ExtensionConfig> = {};

  for (const name of extensionNames) {
    // 获取该扩展的配置
    const config = await readExtensionConfig(name);
    if (config) {
      configs[name] = config;
    }
  }

  return configs;
}

/**
 * 同步更新 extension.config.ts 中的版本号
 *
 * 确保构建配置文件和 dashboard 配置文件的版本号保持一致
 */
export async function syncExtensionConfigVersion(
  extensionName: string,
  newVersion: string
): Promise<void> {
  try {
    const configPath = `packages/extensions/${extensionName}/extension.config.ts`;

    if (!await pathExists(configPath)) {
      logger.warn(`extension.config.ts 不存在，跳过版本同步: ${extensionName}`);
      return;
    }

    // 读取配置文件内容
    const content = await readFile(configPath, 'utf-8');

    // 使用正则表达式替换版本号
    const versionRegex = /version:\s*['"`]([^'"`]+)['"`]/;
    const updatedContent = content.replace(versionRegex, `version: '${newVersion}'`);

    if (content !== updatedContent) {
      await writeFile(configPath, updatedContent, 'utf-8');
      logger.success(`同步 extension.config.ts 版本号: ${extensionName} -> ${newVersion}`);
    } else {
      logger.info(`extension.config.ts 版本号已是最新: ${extensionName}`);
    }
  } catch (error) {
    logger.error(`同步 extension.config.ts 版本号失败: ${extensionName}`, error);
    // 不抛出错误，避免影响主流程
  }
}

/**
 * 格式化日期为 YYYY-MM-DD HH:mm 格式
 */
function formatDate(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}`;
}
