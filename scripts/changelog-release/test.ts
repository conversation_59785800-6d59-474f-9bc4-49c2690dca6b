/**
 * Changelog Release 模块测试
 * 
 * 简单的测试脚本，验证各个模块的基本功能
 */

import { parseConventionalCommits, groupCommitsByExtension } from './commit-parser';
import { determineVersionBump, calculateNextVersion, validateVersion } from './version-calc';
import { generateChangelog } from './file-ops';
import type { GitCommit, ParsedCommit } from './types';

/**
 * 测试数据
 */
const mockCommits: GitCommit[] = [
  {
    hash: 'abc123def456',
    shortHash: 'abc123d',
    author: 'Test User',
    email: '<EMAIL>',
    date: new Date('2024-01-01'),
    message: `feat(cookies): 支持导出 Cookie 为 JSON 文件

新增一个导出按钮，允许用户将当前选中的 Cookie 保存为标准的 JSON 格式。

Applies-To: cookies_manager
Issue-ID: 3#20250711-03, 20250711-04`
  },
  {
    hash: 'def456ghi789',
    shortHash: 'def456g',
    author: 'Test User',
    email: '<EMAIL>',
    date: new Date('2024-01-02'),
    message: `fix(price): 修复价格监控异常

修复了在某些情况下价格监控失效的问题。

Applies-To: price_tracker
Issue-ID: 20250711-05`
  },
  {
    hash: 'ghi789jkl012',
    shortHash: 'ghi789j',
    author: 'Test User',
    email: '<EMAIL>',
    date: new Date('2024-01-03'),
    message: `refactor: 重构公共工具函数

BREAKING CHANGE: 移除了旧的 API 接口

Applies-To: cookies_manager, price_tracker`
  }
];

/**
 * 测试 commit 解析功能
 */
function testCommitParser() {
  console.log('🧪 测试 Commit 解析功能...');
  
  const parsedCommits = parseConventionalCommits(mockCommits);
  console.log(`✅ 解析了 ${parsedCommits.length} 个提交`);
  
  // 验证第一个提交
  const firstCommit = parsedCommits[0];
  if (firstCommit) {
    console.log(`  - 类型: ${firstCommit.type}`);
    console.log(`  - 作用域: ${firstCommit.scope}`);
    console.log(`  - 主题: ${firstCommit.subject}`);
    console.log(`  - 适用插件: ${firstCommit.appliesTo.join(', ')}`);
    console.log(`  - Issue ID: ${firstCommit.issueIds.join(', ')}`);
  }
  
  // 测试分组功能
  const grouped = groupCommitsByExtension(parsedCommits);
  console.log(`✅ 按插件分组: ${Object.keys(grouped).length} 个插件`);
  for (const [extensionName, commits] of Object.entries(grouped)) {
    console.log(`  - ${extensionName}: ${commits.length} 个提交`);
  }
  
  console.log('');
}

/**
 * 测试版本计算功能
 */
function testVersionCalc() {
  console.log('🧪 测试版本计算功能...');
  
  const parsedCommits = parseConventionalCommits(mockCommits);
  
  // 测试版本变更类型计算
  const bump = determineVersionBump(parsedCommits);
  console.log(`✅ 版本变更类型: ${bump}`);
  
  // 测试版本号计算
  const currentVersion = '1.2.3';
  const newVersion = calculateNextVersion(currentVersion, bump);
  console.log(`✅ 版本计算: ${currentVersion} → ${newVersion}`);
  
  // 测试版本号验证
  console.log(`✅ 版本验证 '1.2.3': ${validateVersion('1.2.3')}`);
  console.log(`✅ 版本验证 'invalid': ${validateVersion('invalid')}`);
  
  console.log('');
}

/**
 * 测试 changelog 生成功能
 */
function testChangelogGeneration() {
  console.log('🧪 测试 Changelog 生成功能...');
  
  const parsedCommits = parseConventionalCommits(mockCommits);
  const changelog = generateChangelog('1.3.0', parsedCommits);
  
  console.log('✅ 生成的 Changelog:');
  console.log('---');
  console.log(changelog);
  console.log('---');
  console.log('');
}

/**
 * 测试边界情况
 */
function testEdgeCases() {
  console.log('🧪 测试边界情况...');
  
  // 测试空提交列表
  const emptyParsed = parseConventionalCommits([]);
  console.log(`✅ 空提交列表解析: ${emptyParsed.length} 个结果`);
  
  // 测试无效提交格式
  const invalidCommit: GitCommit = {
    hash: 'invalid123',
    shortHash: 'invalid',
    author: 'Test',
    email: '<EMAIL>',
    date: new Date(),
    message: 'invalid commit message format'
  };
  
  const invalidParsed = parseConventionalCommits([invalidCommit]);
  console.log(`✅ 无效提交格式解析: ${invalidParsed.length} 个结果`);
  
  // 测试无 Applies-To 的提交
  const noAppliesTo: GitCommit = {
    hash: 'noapplies123',
    shortHash: 'noappl1',
    author: 'Test',
    email: '<EMAIL>',
    date: new Date(),
    message: 'feat: some feature without applies-to'
  };
  
  const noAppliesParsed = parseConventionalCommits([noAppliesTo]);
  const noAppliesGrouped = groupCommitsByExtension(noAppliesParsed);
  console.log(`✅ 无 Applies-To 提交分组: ${Object.keys(noAppliesGrouped).length} 个插件`);
  
  console.log('');
}

/**
 * 运行所有测试
 */
async function runTests() {
  console.log('🚀 开始运行 Changelog Release 模块测试\n');
  
  try {
    testCommitParser();
    testVersionCalc();
    testChangelogGeneration();
    testEdgeCases();
    
    console.log('✅ 所有测试通过！');
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runTests();
}

export { runTests };
