/**
 * Changelog Release 模块常量定义
 *
 * 定义核心常量，遵循 KISS 原则
 */

import type { VersionBump } from './types';

/**
 * Conventional Commit 类型定义
 */
export const COMMIT_TYPES = {
  feat: { description: '新功能', changelog: true, bump: 'minor' as VersionBump },
  fix: { description: '修复', changelog: true, bump: 'patch' as VersionBump },
  docs: { description: '文档', changelog: false, bump: 'none' as VersionBump },
  style: { description: '格式', changelog: false, bump: 'none' as VersionBump },
  refactor: { description: '重构', changelog: true, bump: 'patch' as VersionBump },
  perf: { description: '性能', changelog: true, bump: 'patch' as VersionBump },
  test: { description: '测试', changelog: false, bump: 'none' as VersionBump },
  build: { description: '构建', changelog: false, bump: 'none' as VersionBump },
  ci: { description: 'CI', changelog: false, bump: 'none' as VersionBump },
  chore: { description: '杂项', changelog: false, bump: 'none' as VersionBump },
  revert: { description: '回滚', changelog: true, bump: 'patch' as VersionBump },
} as const;

/**
 * 提交类型列表
 */
export const VALID_COMMIT_TYPES = Object.keys(COMMIT_TYPES);

/**
 * 需要包含在 Changelog 中的提交类型
 */
export const CHANGELOG_TYPES = Object.entries(COMMIT_TYPES)
  .filter(([, config]) => config.changelog)
  .map(([type]) => type);

/**
 * 破坏性变更标识
 */
export const BREAKING_CHANGE_KEYWORDS = [
  'BREAKING CHANGE',
  'BREAKING-CHANGE',
  'BREAKING CHANGES',
  'BREAKING-CHANGES',
];

/**
 * 自定义 Footer 键名
 */
export const CUSTOM_FOOTERS = {
  APPLIES_TO: 'Applies-To',
  ISSUE_ID: 'Issue-ID',
} as const;

/**
 * 默认配置
 */
export const DEFAULT_CONFIG = {
  /** 默认基准分支 */
  baseBranch: 'main',
  /** 默认文件编码 */
  encoding: 'utf-8' as BufferEncoding,
  /** JSON 缩进空格数 */
  jsonIndent: 2,
  /** 默认时区 */
  timezone: 'Asia/Shanghai',
} as const;

/**
 * 文件路径模板
 */
export const FILE_PATHS = {
  /** Dashboard 数据文件路径模板 */
  dashboardData: 'packages/extensions/extension_dashboard/extensions/{name}.json',
  /** 扩展配置文件路径模板 */
  extensionConfig: 'packages/extensions/{name}/extension.config.ts',
  /** 发布配置文件名 */
  releaseConfig: 'release-extensions.json',
  /** 发布报告路径模板 */
  releaseReport: '.output/{extensionName}/{version}/RELEASE.md',
} as const;

/**
 * 类型图标映射
 */
export const TYPE_EMOJIS = {
  feat: '✨',
  fix: '🐛',
  docs: '📝',
  style: '💄',
  refactor: '♻️',
  perf: '⚡',
  test: '✅',
  build: '📦',
  ci: '👷',
  chore: '🔧',
  revert: '⏪',
  breaking: '⚠️',
} as const;

/**
 * 正则表达式
 */
export const REGEX_PATTERNS = {
  /** Issue ID 格式 - 匹配完整的 issue id，包括可选的子需求 ID */
  issueId: /(\d+)?#\d{8}-\d{2}/g,
  /** 版本号格式 */
  version: /^\d+\.\d+\.\d+$/,
} as const;

