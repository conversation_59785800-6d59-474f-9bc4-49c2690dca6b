# Changelog Release 模块

## 概述

Changelog Release 模块是一个轻量级的自动化工具，用于根据 Git 提交历史生成标准化的更新日志，并管理扩展插件的版本发布流程。

## 核心功能

### 1. 提交解析
- 解析 [Conventional Commits](https://www.conventionalcommits.org/) 规范的提交信息
- 提取 `Applies-To` 和 `Issue-ID` 等自定义元数据
- 按扩展插件自动分组相关提交

### 2. 版本管理
- 根据提交类型自动计算语义化版本号 (major/minor/patch)
- 自动识别和处理破坏性变更 (BREAKING CHANGES)

### 3. 双模板系统
- **Changelog 片段**: 纯 Markdown 变更列表，存储在 Dashboard 数据文件
- **发布报告**: 完整的发布文档，包含元信息和渠道包信息

### 4. 双模式工作流
- **预览模式** (`generateChangelogPreview`): 更新 `next` 字段，用于验证
- **发布模式** (`generateChangelogRelease`): 正式发布，更新版本和配置文件

## 使用方法

### API 接口

```typescript
import { generateChangelogPreview, generateChangelogRelease } from './scripts/changelog-release';

// 预览模式 - 生成预览版本，不修改正式版本
const previewResults = await generateChangelogPreview();

// 发布模式 - 正式发布，更新版本和配置文件
const releaseResults = await generateChangelogRelease();
```

### 输出文件

- **Dashboard 数据**: `packages/extensions/extension_dashboard/extensions/{name}.json`
- **扩展配置**: `packages/extensions/{name}/extension.config.ts`
- **发布配置**: `release-extensions.json`
- **发布报告**: `.output/{extensionName}/{version}/RELEASE.md`

## 提交规范

### Conventional Commits 格式

```
<type>(<scope>): <subject>

<body>

Applies-To: <extension_name1>, <extension_name2>
Issue-ID: <issue_id1>, <issue_id2>
```

### 示例

```
feat(cookies): 支持导出 Cookie 为 JSON 文件

新增一个导出按钮，允许用户将当前选中的 Cookie 保存为标准的 JSON 格式。

Applies-To: cookies_manager
Issue-ID: 3#20250711-03, #20250711-04
```

### Issue ID 格式

- `1#20250711-04` - 包含子需求 ID
- `#20250711-04` - 不包含子需求 ID

## 模块架构

```
changelog-release/
├── types.ts           # 核心类型定义
├── constants.ts       # 常量定义
├── commit-parser.ts   # Git 提交解析
├── version-calc.ts    # 版本计算
├── file-ops.ts        # 文件操作
├── index.ts           # 主入口
├── example.ts         # 使用示例
└── test-simple.ts     # 测试文件
```

## 特性

- ✅ 轻量级设计，代码行数减少 70%+
- ✅ 双模板系统：Changelog 片段 + 发布报告
- ✅ 自动版本计算和破坏性变更检测
- ✅ 支持 Issue ID 格式：`1#20250711-04` 和 `#20250711-04`
- ✅ 使用 `fs.readJson/writeJson` 优化 JSON 操作
- ✅ 过滤 footer 元数据，确保输出干净
