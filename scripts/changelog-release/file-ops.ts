/**
 * 文件操作模块
 * 
 * 处理文件读写操作，包括读取当前版本、更新 Dashboard 数据、
 * 更新扩展配置、生成发布文件
 */

import * as fs from 'fs-extra';
import { join, dirname } from 'node:path';
import { FILE_PATHS, DEFAULT_CONFIG, TYPE_EMOJIS, COMMIT_TYPES } from './constants';
import type { ExtensionData, ReleaseInfo, ReleaseExtensionsConfig, ParsedCommit } from './types';

/**
 * 从扩展配置文件读取当前版本
 * 
 * @param extensionName - 扩展名称
 * @param cwd - 工作目录
 * @returns 当前版本号
 */
export async function getCurrentVersion(extensionName: string, cwd: string = process.cwd()): Promise<string> {
  try {
    // 先尝试从 Dashboard 数据文件读取
    const dashboardPath = join(cwd, FILE_PATHS.dashboardData.replace('{name}', extensionName));
    if (await fs.pathExists(dashboardPath)) {
      const content = await fs.readFile(dashboardPath, 'utf-8');
      const data: ExtensionData = JSON.parse(content);
      if (data.version) {
        return data.version;
      }
    }

    // 如果 Dashboard 数据文件不存在或没有版本信息，从扩展配置文件读取
    const configPath = join(cwd, FILE_PATHS.extensionConfig.replace('{name}', extensionName));
    if (await fs.pathExists(configPath)) {
      const content = await fs.readFile(configPath, 'utf-8');
      // 简单的正则匹配版本号
      const versionMatch = content.match(/version:\s*['"`]([^'"`]+)['"`]/);
      if (versionMatch) {
        return versionMatch[1];
      }
    }

    // 默认版本
    return '1.0.0';
  } catch (error) {
    console.error(`读取扩展 ${extensionName} 版本失败:`, error);
    return '1.0.0';
  }
}

/**
 * 生成 Markdown 格式的 changelog
 * 
 * @param version - 版本号
 * @param commits - 提交列表
 * @returns Markdown 格式的 changelog
 */
export function generateChangelog(version: string, commits: ParsedCommit[]): string {
  const date = new Date().toISOString().split('T')[0];
  const lines: string[] = [];
  
  lines.push(`## [${version}] - ${date}`);
  lines.push('');
  
  // 按类型分组提交
  const groupedCommits: Record<string, ParsedCommit[]> = {};
  for (const commit of commits) {
    if (!groupedCommits[commit.type]) {
      groupedCommits[commit.type] = [];
    }
    groupedCommits[commit.type].push(commit);
  }
  
  // 按优先级排序类型
  const typeOrder = ['feat', 'fix', 'refactor', 'perf', 'revert'];
  const sortedTypes = Object.keys(groupedCommits).sort((a, b) => {
    const aIndex = typeOrder.indexOf(a);
    const bIndex = typeOrder.indexOf(b);
    if (aIndex === -1 && bIndex === -1) return a.localeCompare(b);
    if (aIndex === -1) return 1;
    if (bIndex === -1) return -1;
    return aIndex - bIndex;
  });
  
  // 生成各类型的变更列表
  for (const type of sortedTypes) {
    const typeCommits = groupedCommits[type];
    const emoji = TYPE_EMOJIS[type as keyof typeof TYPE_EMOJIS] || '📝';
    const typeConfig = COMMIT_TYPES[type as keyof typeof COMMIT_TYPES];
    const title = typeConfig?.description || type;
    
    lines.push(`### ${emoji} ${title}`);
    lines.push('');
    
    for (const commit of typeCommits) {
      const scope = commit.scope ? `**${commit.scope}**: ` : '';
      const issues = commit.issueIds.length > 0 ? ` (${commit.issueIds.join(', ')})` : '';
      lines.push(`- ${scope}${commit.subject}${issues}`);
    }
    lines.push('');
  }
  
  // 添加破坏性变更
  const breakingCommits = commits.filter(c => c.breaking);
  if (breakingCommits.length > 0) {
    lines.push('### ⚠️ BREAKING CHANGES');
    lines.push('');
    for (const commit of breakingCommits) {
      if (commit.breakingNotes) {
        for (const note of commit.breakingNotes) {
          lines.push(`- ${note}`);
        }
      } else {
        lines.push(`- ${commit.subject}`);
      }
    }
    lines.push('');
  }
  
  return lines.join('\n').trim();
}

/**
 * 更新 Dashboard 数据文件的 next 字段（预览模式）
 * 
 * @param extensionName - 扩展名称
 * @param releaseInfo - 发布信息
 * @param cwd - 工作目录
 */
export async function updateDashboardNext(
  extensionName: string, 
  releaseInfo: ReleaseInfo, 
  cwd: string = process.cwd()
): Promise<void> {
  try {
    const filePath = join(cwd, FILE_PATHS.dashboardData.replace('{name}', extensionName));
    
    // 确保目录存在
    await fs.ensureDir(dirname(filePath));

    // 读取现有数据
    let data: ExtensionData = {
      name: extensionName,
      version: releaseInfo.currentVersion
    };

    if (await fs.pathExists(filePath)) {
      const content = await fs.readFile(filePath, 'utf-8');
      data = JSON.parse(content);
    }

    // 更新 next 字段
    data.next = {
      version: releaseInfo.newVersion,
      changelog: releaseInfo.changelog
    };

    // 写入文件
    await fs.writeFile(filePath, JSON.stringify(data, null, DEFAULT_CONFIG.jsonIndent), 'utf-8');
    console.log(`✅ 已更新 ${extensionName} 预览版本: ${releaseInfo.newVersion}`);
  } catch (error) {
    console.error(`更新 Dashboard 数据失败 ${extensionName}:`, error);
    throw error;
  }
}

/**
 * 更新 Dashboard 数据文件的正式版本（发布模式）
 * 
 * @param extensionName - 扩展名称
 * @param releaseInfo - 发布信息
 * @param cwd - 工作目录
 */
export async function updateDashboardRelease(
  extensionName: string, 
  releaseInfo: ReleaseInfo, 
  cwd: string = process.cwd()
): Promise<void> {
  try {
    const filePath = join(cwd, FILE_PATHS.dashboardData.replace('{name}', extensionName));
    
    // 确保目录存在
    await fs.ensureDir(dirname(filePath));

    // 读取现有数据
    let data: ExtensionData = {
      name: extensionName,
      version: releaseInfo.currentVersion
    };

    if (await fs.pathExists(filePath)) {
      const content = await fs.readFile(filePath, 'utf-8');
      data = JSON.parse(content);
    }

    // 更新正式版本信息
    data.version = releaseInfo.newVersion;
    data.changelog = releaseInfo.changelog;
    data.releaseAt = new Date().toISOString();

    // 清空 next 字段
    delete data.next;

    // 写入文件
    await fs.writeFile(filePath, JSON.stringify(data, null, DEFAULT_CONFIG.jsonIndent), 'utf-8');
    console.log(`✅ 已发布 ${extensionName} 版本: ${releaseInfo.newVersion}`);
  } catch (error) {
    console.error(`更新 Dashboard 数据失败 ${extensionName}:`, error);
    throw error;
  }
}

/**
 * 更新扩展配置文件的版本号
 *
 * @param extensionName - 扩展名称
 * @param newVersion - 新版本号
 * @param cwd - 工作目录
 */
export async function updateExtensionConfig(
  extensionName: string,
  newVersion: string,
  cwd: string = process.cwd()
): Promise<void> {
  try {
    const configPath = join(cwd, FILE_PATHS.extensionConfig.replace('{name}', extensionName));

    if (!(await fs.pathExists(configPath))) {
      console.warn(`扩展配置文件不存在: ${configPath}`);
      return;
    }

    // 读取配置文件
    const content = await fs.readFile(configPath, 'utf-8');

    // 替换版本号
    const updatedContent = content.replace(
      /version:\s*['"`]([^'"`]+)['"`]/,
      `version: '${newVersion}'`
    );

    // 写入文件
    await fs.writeFile(configPath, updatedContent, 'utf-8');
    console.log(`✅ 已更新 ${extensionName} 配置文件版本: ${newVersion}`);
  } catch (error) {
    console.error(`更新扩展配置失败 ${extensionName}:`, error);
    throw error;
  }
}

/**
 * 生成发布配置文件 (release-extensions.json)
 *
 * @param releases - 发布信息映射
 * @param cwd - 工作目录
 */
export async function generateReleaseConfig(
  releases: Record<string, ReleaseInfo>,
  cwd: string = process.cwd()
): Promise<void> {
  try {
    const filePath = join(cwd, FILE_PATHS.releaseConfig);

    const config: ReleaseExtensionsConfig = {
      generatedAt: new Date().toISOString(),
      extensions: Object.values(releases).map(release => ({
        name: release.extensionName,
        version: release.newVersion,
        build: true
      }))
    };

    await fs.writeFile(filePath, JSON.stringify(config, null, DEFAULT_CONFIG.jsonIndent), 'utf-8');
    console.log(`✅ 已生成发布配置文件: ${FILE_PATHS.releaseConfig}`);
  } catch (error) {
    console.error('生成发布配置文件失败:', error);
    throw error;
  }
}

/**
 * 生成发布报告 (RELEASE.md)
 *
 * @param extensionName - 扩展名称
 * @param releaseInfo - 发布信息
 * @param cwd - 工作目录
 */
export async function generateReleaseReport(
  extensionName: string,
  releaseInfo: ReleaseInfo,
  cwd: string = process.cwd()
): Promise<void> {
  try {
    const reportPath = join(cwd, FILE_PATHS.releaseReport
      .replace('{extensionName}', extensionName)
      .replace('{version}', releaseInfo.newVersion)
    );

    // 确保目录存在
    await fs.ensureDir(dirname(reportPath));

    // 生成报告内容
    const lines: string[] = [];
    lines.push(`# ${extensionName} v${releaseInfo.newVersion} 发布报告`);
    lines.push('');
    lines.push(`**发布时间**: ${new Date().toLocaleString('zh-CN', { timeZone: DEFAULT_CONFIG.timezone })}`);
    lines.push(`**版本变更**: ${releaseInfo.currentVersion} → ${releaseInfo.newVersion} (${releaseInfo.bump})`);
    lines.push(`**提交数量**: ${releaseInfo.commits.length}`);
    lines.push('');
    lines.push('## 更新日志');
    lines.push('');
    lines.push(releaseInfo.changelog);
    lines.push('');
    lines.push('## 相关提交');
    lines.push('');
    for (const commit of releaseInfo.commits) {
      lines.push(`- ${commit.shortHash}: ${commit.subject}`);
    }

    const content = lines.join('\n');
    await fs.writeFile(reportPath, content, 'utf-8');
    console.log(`✅ 已生成发布报告: ${reportPath}`);
  } catch (error) {
    console.error(`生成发布报告失败 ${extensionName}:`, error);
    throw error;
  }
}
