/**
 * Changelog Release 主入口
 * 
 * 编排核心流程，实现 preview 和 release 两种模式，
 * 协调各个模块完成完整的发布流程
 */

import { getCommitsSinceLastTag, parseConventionalCommits, groupCommitsByExtension } from './commit-parser';
import { calculateVersionInfo } from './version-calc';
import { 
  getCurrentVersion, 
  generateChangelog, 
  updateDashboardNext, 
  updateDashboardRelease,
  updateExtensionConfig,
  generateReleaseConfig,
  generateReleaseReport
} from './file-ops';
import type { ReleaseInfo } from './types';

/**
 * 生成 Changelog（预览模式）
 *
 * 更新插件数据文件的 next 字段，用于构建和验证
 *
 * @returns 发布信息映射
 */
export async function generateChangelogPreview(): Promise<Record<string, ReleaseInfo>> {
  console.log('🔍 开始生成 Changelog 预览...');
  
  try {
    // 1. 获取提交
    console.log('📝 获取 Git 提交历史...');
    const commits = await getCommitsSinceLastTag();
    if (commits.length === 0) {
      console.log('ℹ️  没有找到新的提交');
      return {};
    }
    console.log(`📝 找到 ${commits.length} 个提交`);

    // 2. 解析和分组
    console.log('🔍 解析 Conventional Commits...');
    const parsedCommits = parseConventionalCommits(commits);
    const groupedCommits = groupCommitsByExtension(parsedCommits);

    if (Object.keys(groupedCommits).length === 0) {
      console.log('ℹ️  没有找到需要更新的扩展');
      return {};
    }

    console.log(`🎯 找到 ${Object.keys(groupedCommits).length} 个扩展需要更新:`);
    for (const [extensionName, commits] of Object.entries(groupedCommits)) {
      console.log(`  - ${extensionName}: ${commits.length} 个提交`);
    }

    // 3. 处理每个插件
    const results: Record<string, ReleaseInfo> = {};

    for (const [extensionName, extensionCommits] of Object.entries(groupedCommits)) {
      try {
        console.log(`\n🔧 处理扩展: ${extensionName}`);

        // 获取当前版本
        const currentVersion = await getCurrentVersion(extensionName);
        console.log(`  当前版本: ${currentVersion}`);

        // 计算版本信息
        const versionInfo = calculateVersionInfo(currentVersion, extensionCommits);

        if (!versionInfo.hasChanges) {
          console.log(`  ⏭️  跳过 ${extensionName}，没有需要发布的变更`);
          continue;
        }

        if (!versionInfo.newVersion) {
          console.log(`  ❌ 跳过 ${extensionName}，版本计算失败`);
          continue;
        }

        console.log(`  新版本: ${versionInfo.newVersion} (${versionInfo.bump})`);

        // 生成 changelog
        const changelog = generateChangelog(versionInfo.newVersion, extensionCommits);

        // 创建发布信息
        const releaseInfo: ReleaseInfo = {
          extensionName,
          currentVersion,
          newVersion: versionInfo.newVersion,
          bump: versionInfo.bump,
          commits: extensionCommits,
          changelog
        };

        // 更新 Dashboard 数据文件的 next 字段
        await updateDashboardNext(extensionName, releaseInfo);

        results[extensionName] = releaseInfo;

      } catch (error) {
        console.error(`❌ 处理扩展 ${extensionName} 失败:`, error);
      }
    }
    
    // 4. 输出摘要
    console.log('\n📊 预览摘要:');
    if (Object.keys(results).length === 0) {
      console.log('  没有扩展需要更新');
    } else {
      for (const [extensionName, releaseInfo] of Object.entries(results)) {
        console.log(`  ✅ ${extensionName}: ${releaseInfo.currentVersion} → ${releaseInfo.newVersion}`);
      }
    }
    
    return results;
    
  } catch (error) {
    console.error('❌ 生成 Changelog 预览失败:', error);
    throw error;
  }
}

/**
 * 生成 Changelog（发布模式）
 *
 * 更新插件数据文件的正式版本，同步更新构建配置文件的版本号，
 * 并生成用于驱动构建流程的 release-extensions.json 和发布报告
 *
 * @returns 发布信息映射
 */
export async function generateChangelogRelease(): Promise<Record<string, ReleaseInfo>> {
  console.log('🚀 开始生成正式发布...');
  
  try {
    // 1. 获取提交
    console.log('📝 获取 Git 提交历史...');
    const commits = await getCommitsSinceLastTag();
    if (commits.length === 0) {
      console.log('ℹ️  没有找到新的提交');
      return {};
    }
    console.log(`📝 找到 ${commits.length} 个提交`);

    // 2. 解析和分组
    console.log('🔍 解析 Conventional Commits...');
    const parsedCommits = parseConventionalCommits(commits);
    const groupedCommits = groupCommitsByExtension(parsedCommits);

    if (Object.keys(groupedCommits).length === 0) {
      console.log('ℹ️  没有找到需要更新的扩展');
      return {};
    }

    console.log(`🎯 找到 ${Object.keys(groupedCommits).length} 个扩展需要发布:`);
    for (const [extensionName, commits] of Object.entries(groupedCommits)) {
      console.log(`  - ${extensionName}: ${commits.length} 个提交`);
    }

    // 3. 处理每个插件
    const results: Record<string, ReleaseInfo> = {};

    for (const [extensionName, extensionCommits] of Object.entries(groupedCommits)) {
      try {
        console.log(`\n🔧 处理扩展: ${extensionName}`);

        // 获取当前版本
        const currentVersion = await getCurrentVersion(extensionName);
        console.log(`  当前版本: ${currentVersion}`);

        // 计算版本信息
        const versionInfo = calculateVersionInfo(currentVersion, extensionCommits);

        if (!versionInfo.hasChanges) {
          console.log(`  ⏭️  跳过 ${extensionName}，没有需要发布的变更`);
          continue;
        }

        if (!versionInfo.newVersion) {
          console.log(`  ❌ 跳过 ${extensionName}，版本计算失败`);
          continue;
        }

        console.log(`  新版本: ${versionInfo.newVersion} (${versionInfo.bump})`);

        // 生成 changelog
        const changelog = generateChangelog(versionInfo.newVersion, extensionCommits);

        // 创建发布信息
        const releaseInfo: ReleaseInfo = {
          extensionName,
          currentVersion,
          newVersion: versionInfo.newVersion,
          bump: versionInfo.bump,
          commits: extensionCommits,
          changelog
        };

        // 更新 Dashboard 数据文件
        await updateDashboardRelease(extensionName, releaseInfo);

        // 更新扩展配置文件版本号
        await updateExtensionConfig(extensionName, versionInfo.newVersion);

        // 生成发布报告
        await generateReleaseReport(extensionName, releaseInfo);

        results[extensionName] = releaseInfo;

      } catch (error) {
        console.error(`❌ 处理扩展 ${extensionName} 失败:`, error);
      }
    }

    // 4. 生成发布配置文件
    if (Object.keys(results).length > 0) {
      console.log('\n📦 生成发布配置文件...');
      await generateReleaseConfig(results);
    }
    
    // 5. 输出摘要
    console.log('\n📊 发布摘要:');
    if (Object.keys(results).length === 0) {
      console.log('  没有扩展需要发布');
    } else {
      for (const [extensionName, releaseInfo] of Object.entries(results)) {
        console.log(`  🚀 ${extensionName}: ${releaseInfo.currentVersion} → ${releaseInfo.newVersion}`);
      }
      console.log(`\n✅ 共发布 ${Object.keys(results).length} 个扩展`);
      console.log('📝 请手动提交变更的文件并打上版本标签');
    }
    
    return results;
    
  } catch (error) {
    console.error('❌ 生成正式发布失败:', error);
    throw error;
  }
}

// 导出类型
export type { ReleaseInfo, ParsedCommit, VersionBump } from './types';
