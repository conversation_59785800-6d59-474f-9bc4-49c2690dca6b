# Changelog-Release 重构计划

## 一、现有实现分析

### 1.1 当前架构问题

**过度复杂化**：
- 540 行的 `index.ts` 包含过多抽象层
- 8 个文件分工不清，职责重叠
- 复杂的数据流转和函数调用链

**设计问题**：
- Preview 和 Release 模式重复逻辑
- 过度的错误处理和日志记录
- 不必要的配置抽象

**文件职责混乱**：
- `parser.ts` (402行) - 过度复杂的解析逻辑
- `generator.ts` (494行) - 混合了读取、生成、写入多种职责
- `utils.ts` - 工具函数散乱

### 1.2 核心功能理解

**实际需求**：
1. 从 Git commits 解析 conventional commits
2. 按 `Applies-To` 字段分组到不同插件
3. 计算语义化版本号 (major/minor/patch)
4. 生成 Markdown changelog
5. 更新两个文件：
   - `extension_dashboard/extensions/{name}.json` (数据文件)
   - `packages/extensions/{name}/extension.config.ts` (配置文件)
6. 生成 `release-extensions.json` 和 `RELEASE.md`

**文件关系**：
- `extension.config.ts` - 构建配置，包含当前版本号
- `{name}.json` - Dashboard 数据，存储版本历史和 changelog
- 两者需要版本号同步

## 二、重写方案

### 2.1 设计原则

**KISS 原则**：
- 单一入口函数
- 直接的数据流
- 最少的抽象层

**YAGNI 原则**：
- 去掉过度的错误处理
- 简化日志记录
- 移除不必要的配置选项

**模块化**：
- 每个文件单一职责
- 清晰的函数边界
- 最小化依赖

### 2.2 新架构设计

```
changelog-release/
├── index.ts           # 主入口 (~100行)
├── commit-parser.ts   # Commit 解析 (~80行)
├── version-calc.ts    # 版本计算 (~60行)
├── file-ops.ts        # 文件操作 (~120行)
├── types.ts           # 类型定义 (~50行)
└── constants.ts       # 常量定义 (~40行)
```

### 2.3 核心流程

```typescript
// index.ts - 主流程
export async function generateChangelog(mode: 'preview' | 'release') {
  // 1. 获取 commits (15行)
  const commits = await getCommitsSinceLastTag()
  
  // 2. 解析和分组 (20行)
  const parsedCommits = parseConventionalCommits(commits)
  const groupedByExtension = groupCommitsByExtension(parsedCommits)
  
  // 3. 处理每个插件 (30行)
  const results = {}
  for (const [extensionName, commits] of groupedByExtension) {
    const currentVersion = await getCurrentVersion(extensionName)
    const newVersion = calculateNextVersion(currentVersion, commits)
    const changelog = generateChangelog(commits)
    results[extensionName] = { currentVersion, newVersion, changelog }
  }
  
  // 4. 写入文件 (25行)
  if (mode === 'preview') {
    await updateDashboardNext(results)
  } else {
    await updateDashboardRelease(results)
    await updateExtensionConfigs(results)
    await generateReleaseFiles(results)
  }
  
  // 5. 输出结果 (10行)
  return results
}
```

### 2.4 模块职责

**commit-parser.ts**：
- `parseConventionalCommits()` - 使用 conventional-commits-parser
- `groupCommitsByExtension()` - 按 Applies-To 分组
- `extractMetadata()` - 提取 Issue-ID 等元数据

**version-calc.ts**：
- `calculateNextVersion()` - 根据 commit 类型计算版本
- `determineVersionBump()` - 判断 major/minor/patch
- `validateVersion()` - 版本号验证

**file-ops.ts**：
- `getCurrentVersion()` - 从 extension.config.ts 读取版本
- `updateDashboardData()` - 更新 Dashboard 数据文件
- `updateExtensionConfig()` - 更新构建配置文件
- `generateReleaseFiles()` - 生成发布文件

## 三、实现细节

### 3.1 依赖库

**保留**：
- `conventional-commits-parser` - Commit 解析
- `semver` - 版本号操作
- `fs-extra` - 文件操作

**移除**：
- 复杂的 Git 操作库
- 过度的日志库依赖

### 3.2 错误处理

**简化策略**：
- 只处理关键错误
- 使用简单的 try-catch
- 减少防御性编程

### 3.3 配置简化

**移除复杂配置**：
- 去掉过多的命令行选项
- 使用合理的默认值
- 减少可配置项

## 四、迁移计划

### 4.1 实现步骤

1. **创建新的简化版本** (scripts.v3/commitizen-changelog)
2. **保持接口兼容** - 相同的输入输出格式
3. **逐步替换** - 先并行运行，验证结果一致
4. **完全替换** - 移除旧实现

### 4.2 验证方案

**功能验证**：
- 相同输入产生相同输出
- 版本计算逻辑一致
- 文件更新结果相同

**性能验证**：
- 执行时间对比
- 内存使用对比
- 代码复杂度对比

## 五、预期收益

### 5.1 代码质量

- **代码行数减少 70%** (从 ~2000行 到 ~450行)
- **文件数量减少** (从 8个 到 5个)
- **复杂度降低** - 每个函数职责单一

### 5.2 维护性

- **易于理解** - 直接的数据流
- **易于调试** - 简单的调用链
- **易于扩展** - 清晰的模块边界

### 5.3 可靠性

- **减少 Bug** - 更少的抽象层
- **更好测试** - 纯函数易于测试
- **更快执行** - 减少不必要的操作

## 六、风险评估

### 6.1 兼容性风险

**低风险**：
- 保持相同的输入输出接口
- 使用相同的依赖库
- 相同的文件操作逻辑

### 6.2 功能风险

**中等风险**：
- 需要仔细验证边界情况
- 确保版本计算逻辑正确
- 保证文件更新的原子性

### 6.3 迁移风险

**低风险**：
- 可以并行运行验证
- 保留旧实现作为备份
- 逐步迁移降低风险
