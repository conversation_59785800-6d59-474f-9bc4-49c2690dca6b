/**
 * Changelog Release 使用示例
 * 
 * 演示如何使用重写后的 changelog-release 模块
 */

import { generateChangelogPreview, generateChangelogRelease } from './index';

/**
 * 示例：生成预览版本
 */
async function examplePreview() {
  console.log('📋 示例：生成 Changelog 预览\n');
  
  try {
    const results = await generateChangelogPreview();
    
    if (Object.keys(results).length === 0) {
      console.log('没有找到需要更新的扩展');
    } else {
      console.log('预览结果:');
      for (const [extensionName, releaseInfo] of Object.entries(results)) {
        console.log(`\n🔍 ${extensionName}:`);
        console.log(`  版本: ${releaseInfo.currentVersion} → ${releaseInfo.newVersion}`);
        console.log(`  变更类型: ${releaseInfo.bump}`);
        console.log(`  提交数: ${releaseInfo.commits.length}`);
      }
    }
  } catch (error) {
    console.error('预览失败:', error);
  }
}

/**
 * 示例：生成正式发布
 */
async function exampleRelease() {
  console.log('\n📋 示例：生成正式发布\n');
  
  try {
    const results = await generateChangelogRelease();
    
    if (Object.keys(results).length === 0) {
      console.log('没有找到需要发布的扩展');
    } else {
      console.log('发布结果:');
      for (const [extensionName, releaseInfo] of Object.entries(results)) {
        console.log(`\n🚀 ${extensionName}:`);
        console.log(`  版本: ${releaseInfo.currentVersion} → ${releaseInfo.newVersion}`);
        console.log(`  变更类型: ${releaseInfo.bump}`);
        console.log(`  提交数: ${releaseInfo.commits.length}`);
      }
      
      console.log('\n✅ 发布完成！');
      console.log('📝 请记得：');
      console.log('  1. 检查生成的文件');
      console.log('  2. 提交变更到 Git');
      console.log('  3. 打上版本标签');
    }
  } catch (error) {
    console.error('发布失败:', error);
  }
}

/**
 * 运行示例
 */
async function runExamples() {
  console.log('🎯 Changelog Release 模块使用示例\n');
  console.log('注意：此示例需要在有 Git 仓库的环境中运行\n');
  
  // 只运行预览示例，避免实际修改文件
  await examplePreview();
  
  console.log('\n💡 提示：');
  console.log('  - 使用 generateChangelogPreview() 生成预览');
  console.log('  - 使用 generateChangelogRelease() 生成正式发布');
  console.log('  - 确保提交信息遵循 Conventional Commits 规范');
  console.log('  - 使用 Applies-To 字段指定影响的插件');
}

// 如果直接运行此文件，执行示例
if (import.meta.url === `file://${process.argv[1]}`) {
  runExamples();
}

export { examplePreview, exampleRelease };
