/**
 * Commit 解析模块
 * 
 * 使用 conventional-commits-parser 解析 Git commits，
 * 提取 Applies-To 和 Issue-ID 元数据，按插件分组
 */

import { execa } from 'execa';
import { CommitParser } from 'conventional-commits-parser';
import { projectPaths } from '../helpers/index';
import {
  COMMIT_TYPES,
  BREAKING_CHANGE_KEYWORDS,
  CUSTOM_FOOTERS,
  REGEX_PATTERNS
} from './constants';
import type { GitCommit, ParsedCommit } from './types';

/**
 * 获取自上次标签以来的所有提交
 *
 * @returns Git 提交列表
 */
export async function getCommitsSinceLastTag(): Promise<GitCommit[]> {
  try {
    // 获取最新标签
    let range = 'HEAD';
    try {
      const { stdout: latestTag } = await execa('git', ['describe', '--tags', '--abbrev=0'], { cwd: projectPaths.workspace });
      if (latestTag) {
        range = `${latestTag}..HEAD`;
      }
    } catch {
      // 如果没有标签，获取所有提交
      range = 'HEAD';
    }

    // 获取提交列表
    const { stdout } = await execa('git', [
      'log',
      '--format=%H|%h|%an|%ae|%at|%B%x00',
      range
    ], { cwd: projectPaths.workspace });

    if (!stdout.trim()) {
      return [];
    }

    const commitStrings = stdout.trim().split('\x00').filter(Boolean);
    const commits: GitCommit[] = [];

    for (const commitString of commitStrings) {
      const lines = commitString.trim().split('\n');
      const [hash, shortHash, author, email, timestamp] = lines[0].split('|');
      const message = lines.slice(1).join('\n').trim();

      commits.push({
        hash,
        shortHash,
        author,
        email,
        date: new Date(parseInt(timestamp) * 1000),
        message,
      });
    }

    return commits;
  } catch (error) {
    console.error('获取 Git 提交失败:', error);
    return [];
  }
}

/**
 * 解析 Conventional Commits
 * 
 * @param commits - Git 提交列表
 * @returns 解析后的提交列表
 */
export function parseConventionalCommits(commits: GitCommit[]): ParsedCommit[] {
  const parsedCommits: ParsedCommit[] = [];

  for (const commit of commits) {
    const parsed = parseGitCommit(commit);
    if (parsed) {
      parsedCommits.push(parsed);
    }
  }

  return parsedCommits;
}

/**
 * 解析单个 Git 提交
 *
 * @param commit - Git 提交
 * @returns 解析后的提交，如果无效则返回 null
 */
function parseGitCommit(commit: GitCommit): ParsedCommit | null {
  try {
    // 使用 conventional-commits-parser 解析
    const commitParser = new CommitParser({
      headerPattern: /^(\w+)(?:\(([^)]+)\))?: (.+)$/,
      headerCorrespondence: ['type', 'scope', 'subject'],
      noteKeywords: BREAKING_CHANGE_KEYWORDS,
    });

    const parsed = commitParser.parse(commit.message);

    if (!parsed) {
      return null;
    }

    // 验证提交类型
    const type = parsed.type?.toLowerCase();
    if (!type || !COMMIT_TYPES[type as keyof typeof COMMIT_TYPES]) {
      return null;
    }

    // 提取自定义元数据
    const footers = extractFooters(commit.message);
    const appliesTo = extractAppliesTo(footers);
    const issueIds = extractIssueIds(footers);

    // 检查是否有破坏性变更
    const breaking = hasBreakingChange(parsed, footers);
    const breakingNotes = extractBreakingNotes(parsed, footers);

    return {
      ...commit,
      type,
      scope: parsed.scope || undefined,
      subject: parsed.subject || '',
      body: parsed.body || undefined,
      breaking,
      breakingNotes: breakingNotes.length > 0 ? breakingNotes : undefined,
      appliesTo,
      issueIds,
      footers,
    };
  } catch (error) {
    console.error(`解析提交失败 ${commit.shortHash}:`, error);
    return null;
  }
}

/**
 * 提取 footer 信息
 */
function extractFooters(message: string): Record<string, string> {
  const footers: Record<string, string> = {};
  const lines = message.split('\n');
  
  for (const line of lines) {
    const match = line.match(/^([A-Za-z-]+):\s*(.+)$/);
    if (match) {
      const [, key, value] = match;
      footers[key] = value.trim();
    }
  }
  
  return footers;
}

/**
 * 提取 Applies-To 信息
 */
function extractAppliesTo(footers: Record<string, string>): string[] {
  const appliesTo = footers[CUSTOM_FOOTERS.APPLIES_TO] || '';
  if (!appliesTo) {
    return [];
  }
  
  return appliesTo
    .split(',')
    .map(item => item.trim())
    .filter(Boolean);
}

/**
 * 提取 Issue-ID 信息
 */
function extractIssueIds(footers: Record<string, string>): string[] {
  const issueId = footers[CUSTOM_FOOTERS.ISSUE_ID] || '';
  if (!issueId) {
    return [];
  }
  
  const matches = issueId.match(REGEX_PATTERNS.issueId);
  return matches || [];
}

/**
 * 检查是否有破坏性变更
 */
function hasBreakingChange(parsed: any, footers: Record<string, string>): boolean {
  // 检查 footer 中的破坏性变更
  for (const keyword of BREAKING_CHANGE_KEYWORDS) {
    if (footers[keyword]) {
      return true;
    }
  }
  
  // 检查解析结果中的破坏性变更
  return parsed.notes?.some((note: any) => 
    BREAKING_CHANGE_KEYWORDS.includes(note.title)
  ) || false;
}

/**
 * 提取破坏性变更说明
 *
 * 只提取纯净的破坏性变更描述，过滤掉 footer 元数据
 */
function extractBreakingNotes(parsed: any, footers: Record<string, string>): string[] {
  const notes: string[] = [];

  // 从 footer 中提取
  for (const keyword of BREAKING_CHANGE_KEYWORDS) {
    if (footers[keyword]) {
      // 清理 footer 内容，移除其他元数据
      const cleanNote = cleanBreakingNote(footers[keyword]);
      if (cleanNote) {
        notes.push(cleanNote);
      }
    }
  }

  // 从解析结果中提取
  if (parsed.notes) {
    for (const note of parsed.notes) {
      if (BREAKING_CHANGE_KEYWORDS.includes(note.title)) {
        const cleanNote = cleanBreakingNote(note.text);
        if (cleanNote) {
          notes.push(cleanNote);
        }
      }
    }
  }

  return notes;
}

/**
 * 清理破坏性变更说明，移除 footer 元数据
 *
 * @param note - 原始破坏性变更说明
 * @returns 清理后的说明
 */
function cleanBreakingNote(note: string): string {
  if (!note) return '';

  // 按行分割
  const lines = note.split('\n');
  const cleanLines: string[] = [];

  for (const line of lines) {
    const trimmedLine = line.trim();

    // 跳过空行
    if (!trimmedLine) continue;

    // 跳过 footer 格式的行 (Key: Value)
    if (/^[A-Za-z-]+:\s*.+$/.test(trimmedLine)) {
      continue;
    }

    cleanLines.push(trimmedLine);
  }

  return cleanLines.join(' ').trim();
}

/**
 * 按扩展插件分组提交
 * 
 * @param commits - 解析后的提交列表
 * @returns 按插件分组的提交映射
 */
export function groupCommitsByExtension(commits: ParsedCommit[]): Record<string, ParsedCommit[]> {
  const groups: Record<string, ParsedCommit[]> = {};
  
  for (const commit of commits) {
    // 只处理需要包含在 changelog 中的提交类型
    const commitConfig = COMMIT_TYPES[commit.type as keyof typeof COMMIT_TYPES];
    if (!commitConfig?.changelog) {
      continue;
    }
    
    // 如果没有指定 Applies-To，跳过（可以后续手动关联）
    if (commit.appliesTo.length === 0) {
      continue;
    }
    
    // 将提交添加到对应的插件组中
    for (const extensionName of commit.appliesTo) {
      if (!groups[extensionName]) {
        groups[extensionName] = [];
      }
      groups[extensionName].push(commit);
    }
  }
  
  return groups;
}
