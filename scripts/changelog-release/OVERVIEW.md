# Changelog Release 模块

## 重写成果

### 代码简化
- **文件数量**: 从 8 个减少到 5 个核心文件
- **代码行数**: 减少 70%+ (从 ~2000 行到 ~450 行)
- **复杂度**: 大幅降低，遵循 KISS 和 YAGNI 原则

### 架构设计
- **模块化**: 清晰的职责分工，每个文件专注单一功能
- **简洁性**: 遵循 KISS 原则，避免过度工程化
- **可维护性**: 纯函数设计，易于测试和扩展

## 核心功能

### 双模式工作流
- **预览模式**: 更新 `next` 字段，用于验证
- **发布模式**: 正式发布，更新版本和配置文件

### 双模板系统
- **Changelog 片段**: 纯 Markdown 变更列表
- **发布报告**: 完整的发布文档，包含元信息

## 使用方法

```typescript
import { generateChangelogPreview, generateChangelogRelease } from './scripts/changelog-release';

// 预览模式
const previewResults = await generateChangelogPreview();

// 发布模式
const releaseResults = await generateChangelogRelease();
```

## 主要特性

- ✅ **轻量级**: 代码行数减少 70%+，遵循 KISS 原则
- ✅ **双模板系统**: Changelog 片段 + 发布报告
- ✅ **双模式工作流**: 预览模式 + 发布模式
- ✅ **自动版本计算**: 支持 major/minor/patch 和破坏性变更
- ✅ **Issue ID 支持**: `1#20250711-04` 和 `#20250711-04` 格式
- ✅ **JSON 优化**: 使用 `fs.readJson/writeJson`
- ✅ **输出优化**: 过滤 footer 元数据，确保输出干净

## 输出文件

- **Dashboard 数据**: `packages/extensions/extension_dashboard/extensions/{name}.json`
- **扩展配置**: `packages/extensions/{name}/extension.config.ts`
- **发布配置**: `release-extensions.json`
- **发布报告**: `.output/{extensionName}/{version}/RELEASE.md`
