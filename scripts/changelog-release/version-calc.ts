/**
 * 版本计算模块
 * 
 * 根据 commit 类型计算语义化版本号，支持 major/minor/patch 版本变更判断
 */

import { valid, inc } from 'semver';
import { COMMIT_TYPES } from './constants';
import type { ParsedCommit, VersionBump } from './types';

/**
 * 计算版本变更类型
 * 
 * @param commits - 提交列表
 * @returns 版本变更类型
 */
export function determineVersionBump(commits: ParsedCommit[]): VersionBump {
  let bump: VersionBump = 'none';
  
  for (const commit of commits) {
    // 如果有破坏性变更，直接返回 major
    if (commit.breaking) {
      return 'major';
    }
    
    // 根据提交类型确定版本变更
    const commitConfig = COMMIT_TYPES[commit.type as keyof typeof COMMIT_TYPES];
    if (!commitConfig) {
      continue;
    }
    
    const commitBump = commitConfig.bump;
    
    // 按优先级更新版本变更类型
    if (commitBump === 'minor' && (bump === 'none' || bump === 'patch')) {
      bump = 'minor';
    } else if (commitBump === 'patch' && bump === 'none') {
      bump = 'patch';
    }
  }
  
  return bump;
}

/**
 * 计算下一个版本号
 * 
 * @param currentVersion - 当前版本号
 * @param bump - 版本变更类型
 * @returns 新版本号，如果计算失败返回 null
 */
export function calculateNextVersion(currentVersion: string, bump: VersionBump): string | null {
  if (bump === 'none') {
    return currentVersion;
  }
  
  if (!validateVersion(currentVersion)) {
    console.error(`无效的版本号: ${currentVersion}`);
    return null;
  }
  
  const nextVersion = inc(currentVersion, bump);
  if (!nextVersion) {
    console.error(`计算新版本失败: ${currentVersion} + ${bump}`);
    return null;
  }
  
  return nextVersion;
}

/**
 * 验证版本号格式
 * 
 * @param version - 版本号
 * @returns 是否有效
 */
export function validateVersion(version: string): boolean {
  return valid(version) !== null;
}

/**
 * 为扩展计算版本信息
 * 
 * @param currentVersion - 当前版本号
 * @param commits - 相关提交列表
 * @returns 版本信息对象
 */
export function calculateVersionInfo(currentVersion: string, commits: ParsedCommit[]) {
  const bump = determineVersionBump(commits);
  const newVersion = calculateNextVersion(currentVersion, bump);
  
  return {
    currentVersion,
    newVersion,
    bump,
    hasChanges: bump !== 'none',
    commits
  };
}
