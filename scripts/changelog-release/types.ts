/**
 * Changelog Release 模块类型定义
 *
 * 定义核心数据结构，遵循 KISS 原则，只保留必要的类型
 */

/**
 * Git 提交信息
 */
export interface GitCommit {
  /** 提交哈希 */
  hash: string;
  /** 简短哈希 (前7位) */
  shortHash: string;
  /** 作者名称 */
  author: string;
  /** 作者邮箱 */
  email: string;
  /** 提交日期 */
  date: Date;
  /** 原始提交信息 */
  message: string;
}

/**
 * 解析后的 Conventional Commit
 */
export interface ParsedCommit extends GitCommit {
  /** 提交类型 (feat, fix, docs, etc.) */
  type: string;
  /** 作用域 */
  scope?: string;
  /** 主题描述 */
  subject: string;
  /** 正文内容 */
  body?: string;
  /** 是否包含破坏性变更 */
  breaking: boolean;
  /** 破坏性变更说明 */
  breakingNotes?: string[];
  /** 适用的插件列表 */
  appliesTo: string[];
  /** 关联的 Issue ID 列表 */
  issueIds: string[];
  /** 原始的 footer 内容 */
  footers: Record<string, string>;
}

/**
 * 版本变更类型
 */
export type VersionBump = 'major' | 'minor' | 'patch' | 'none';

/**
 * 插件数据文件结构 (Dashboard 数据文件)
 */
export interface ExtensionData {
  /** 插件名称 */
  name: string;
  /** 插件显示名称 */
  displayName?: string;
  /** 当前版本 */
  version: string;
  /** 插件图标 */
  icon?: string;
  /** 更新日志 */
  changelog?: string;
  /** 发布时间 */
  releaseAt?: string;
  /** 下一版本预览信息 */
  next?: {
    /** 预计版本号 */
    version: string;
    /** 预计更新日志 */
    changelog: string;
  };
}

/**
 * 更新日志条目
 */
export interface ChangelogEntry {
  /** 版本号 */
  version: string;
  /** 发布日期 */
  date: string;
  /** 变更列表 */
  changes: ChangeItem[];
}

/**
 * 变更项
 */
export interface ChangeItem {
  /** 变更类型 */
  type: string;
  /** 作用域 */
  scope?: string;
  /** 描述 */
  description: string;
  /** 是否为破坏性变更 */
  breaking?: boolean;
  /** 相关提交 */
  commits: string[];
  /** 关联的 Issue */
  issues?: string[];
}

/**
 * 发布信息
 */
export interface ReleaseInfo {
  /** 插件名称 */
  extensionName: string;
  /** 当前版本 */
  currentVersion: string;
  /** 新版本 */
  newVersion: string;
  /** 版本变更类型 */
  bump: VersionBump;
  /** 相关提交列表 */
  commits: ParsedCommit[];
  /** 更新日志 */
  changelog: string;
}

/**
 * 发布扩展配置项 (release-extensions.json 中的单个项目)
 */
export interface ReleaseExtensionItem {
  /** 扩展名称 */
  extensionName: string;
  /** 渠道目标列表 */
  variantTargets: string[];
  /** 版本号 */
  version: string;
  /** 更新日志 */
  changelog: string;
}

/**
 * Git 标签信息
 */
export interface GitTag {
  /** 标签名称 */
  name: string;
  /** 提交哈希 */
  commit: string;
  /** 标签日期 */
  date: Date;
}
