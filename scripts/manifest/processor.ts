/**
 * @fileoverview 插件 manifest 配置处理器
 * @description 独立实现插件 manifest 数据的合并、字段自动填充、URL 生成等核心逻辑。
 *
 * 术语说明：
 * - 插件：指 browser extension 项目。
 * - 渠道包（variant/variants）：指插件的不同分发渠道或构建包。
 * - 插件名字（extension name）：指插件项目的名称。
 */

import { isEmpty } from 'lodash-es';
import { createLogger, deepMerge, compileTemplate } from '../helpers/index.js';
import { WEBSTORES_UPDATE_URL } from '../helpers/constants.js';
import type { ManifestProcessOptions, ProcessedManifestData } from './types.js';
import { cleanManifest } from './utils.js';

const logger = createLogger('ManifestProcessor');

// #region --- 主处理器 ---

/**
 * 处理插件 manifest 数据（主处理器）
 *
 * 主要职责：
 * 1. 合并 baseManifest 与 overrideManifest
 * 2. 自动填充 version、manifest_version、default_locale 等核心字段
 * 3. 生成 homepage_url、update_url 等动态字段
 *
 * @param options - manifest 处理选项
 * @returns 处理后的 manifest 对象
 */
export function processManifestData(options: ManifestProcessOptions): ProcessedManifestData {
  const {
    extensionName,
    version,
    manifestVersion,
    defaultLocale,
    variantInfo,
    baseManifest = {},
    overrideManifest = {},
  } = options;

  logger.info(`开始处理 manifest 数据: ${extensionName} - ${variantInfo.variantTarget}`);

  // 1. 深度合并 manifest 配置
  let merged = deepMerge(baseManifest, overrideManifest);

  // 2. 自动填充必需字段
  merged.version = version;
  merged.manifest_version = manifestVersion;
  merged.default_locale = defaultLocale;

  // 3. 生成 homepage_url
  const homepageUrl = compileTemplate(
    'https://www.aliprice.com?ext_id={variantID}&platform={variantName}&channel={channel}&browser={webstore}&version={version}&mv={manifestVersion}',
    {
      variantID: variantInfo.variantId,
      variantName: variantInfo.variantName,
      channel: variantInfo.variantChannel,
      webstore: variantInfo.webstore,
      version: version,
      manifestVersion: manifestVersion,
    },
  );
  merged.homepage_url = homepageUrl;

  // 4. 生成 update_url
  let updateUrl: string | undefined;
  if (variantInfo.webstore && WEBSTORES_UPDATE_URL[variantInfo.webstore]) {
    updateUrl = compileTemplate(WEBSTORES_UPDATE_URL[variantInfo.webstore], {
      extensionName: variantInfo.variantName,
    });
    merged.update_url = updateUrl;
  }

  // 5. 清理空值
  merged = cleanManifest(merged);


  logger.success(`manifest 数据处理完成: ${extensionName} - ${variantInfo.variantTarget}`);

  return merged as ProcessedManifestData;
}

// #endregion
