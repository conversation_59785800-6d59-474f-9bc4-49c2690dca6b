# helpers 模块 PRD 文档

## 一、背景与目标

在多扩展、多平台的自动化构建与脚本开发中，存在大量通用需求：路径管理、类型约束、日志输出、对象合并、目录校验等。为避免重复造轮子、提升开发效率与一致性，需沉淀一套高复用、易维护的辅助工具模块。

本模块致力于为所有脚本和主流程提供统一的工具函数、常量、类型和日志能力，支撑复杂流程的自动化和可观测性。

## 二、设计原则

1. **高内聚低耦合**：各工具函数职责单一，便于独立测试和维护。
2. **类型安全**：所有导出均有完善 TypeScript 类型定义。
3. **可扩展性**：便于后续新增工具函数和常量。
4. **跨模块复用**：所有脚本和主流程均可直接依赖本模块能力。

## 三、核心功能

1. **路径与目录管理**：统一管理项目根路径、扩展目录、输出目录等，支持目录校验与自动创建。
2. **对象合并与深拷贝**：安全合并配置对象，支持深度合并、可选不可变等参数。
3. **日志输出**：多级别日志（info/warn/error/success/verbose），支持模块名标记，便于排查问题。
4. **通用匹配与模板处理**：支持通配符/正则字符串匹配、URL模板变量替换等。
5. **类型与常量**：集中管理所有脚本用到的类型和常量，便于全局维护。

## 四、目录结构与职责

| 文件/目录      | 说明                         |
|---------------|------------------------------|
| constants.ts  | 常量定义，平台/变体/路径等   |
| index.ts      | 模块主入口，统一导出         |
| logger.ts     | 日志工具，支持多级别输出     |
| types.ts      | 类型定义与约束               |
| utils.ts      | 通用工具函数                 |

## 五、核心流程说明

1. **路径管理流程**
   - 通过 `projectPaths` 统一获取各类目录路径
   - 使用 `checkDirExists` 校验目录存在性，`ensureDirExists` 自动创建目录

2. **对象合并流程**
   - 调用 `deepMerge(target, source, options)`，支持深度合并与不可变
   - 用于配置、manifest、i18n 等多场景

3. **日志输出流程**
   - 通过 `createLogger(moduleName)` 创建带模块名的日志实例
   - 支持 info/warn/error/success/verbose 多级别输出
   - 日志输出统一格式，便于定位问题

4. **通用匹配与模板处理**
   - `simpleMatch(str, pattern)` 支持通配符和正则
   - `compileTemplate(template, params)` 支持 URL 变量替换

## 六、主要接口说明

### projectPaths
> 统一的路径常量对象，包含 workspace、extensions、output 等。

### checkDirExists(dirPath: string): void
> 校验目录是否存在，不存在则抛错。

### ensureDirExists(dirPath: string): void
> 确保目录存在，不存在则自动创建。

### deepMerge<T>(target: T, source: Partial<T>, options?): T
> 安全合并对象，支持深度合并、不可变、数组合并等参数。

### createLogger(moduleName: string)
> 创建带模块名的日志实例，支持多级别输出。

### simpleMatch(str: string, pattern: string): boolean
> 支持通配符和正则的字符串匹配。

### compileTemplate(template: string, params: object, ignoreUndefined?): string
> URL 模板变量替换。

## 七、使用示例

```ts
import { projectPaths, checkDirExists, deepMerge, createLogger } from 'scripts/helpers';

const logger = createLogger('MyScript');
logger.info('开始执行脚本');

checkDirExists(projectPaths.extensions);

const merged = deepMerge({ a: 1 }, { b: 2 });
logger.success('合并结果', merged);
```

## 八、常见问题 FAQ

**Q: 如何新增工具函数？**
A: 在 utils.ts 中实现并补充类型定义，index.ts 导出，补充单元测试或用例。

**Q: 日志如何全局统一？**
A: 所有日志均通过 createLogger 创建，输出格式统一，便于全局检索。

**Q: 路径常量如何维护？**
A: 所有路径常量集中于 projectPaths，变更需同步通知相关模块。

## 九、维护与扩展约定

1. 类型定义集中于 types.ts，变更需同步文档。
2. 新增工具函数需补充注释与用例。
3. 日志输出需带模块名，便于排查。
4. 目录结构如有调整需同步通知相关团队。

---
如有更多问题或建议，请联系维护人或提交 issue。
