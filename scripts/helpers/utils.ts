import fs from 'fs-extra';
import { merge } from 'lodash-es';
import path from 'path';
import type { DeepMergeOptions } from './types.js';

const ROOT_DIR = process.cwd();

/**
 * 通配符字符串匹配，支持 '*' 及 `/pattern/flags` 正则表达式形式。
 *
 * @param {string} str - 需要测试的字符串。
 * @param {string} pattern - 通配符或正则模式。
 *   - '*' 表示通配符。
 *   - 以斜杠开头和结尾的字符串（如 '\/_menu_.*\/ig'）表示正则表达式。
 * @returns {boolean} 是否匹配。
 * @example
 *   simpleMatch('menu_abc', '*abc') // true
 *   simpleMatch('menu_abc', '\/_menu_.*\/ig') // true
 */
export function simpleMatch(str: string, pattern: string): boolean {
  // 支持 /pattern/flags 形式
  if (pattern.startsWith('/')) {
    const lastSlash = pattern.lastIndexOf('/');
    if (lastSlash > 0) {
      const regexBody = pattern.slice(1, lastSlash);
      const regexFlags = pattern.slice(lastSlash + 1);
      try {
        const regex = new RegExp(regexBody, regexFlags);
        return regex.test(str);
      } catch {
        // fallback to通配符
      }
    }
  }

  const regex = new RegExp(`^${pattern.replace(/\*/g, '.*')}$`);
  return regex.test(str);
}

/**
 * @description 根据 include/exclude 模式检查 key 是否匹配
 * @param key 要检查的 key
 * @param include 包含模式列表
 * @param exclude 排除模式列表
 * @returns 如果 key 匹配则返回 true，否则返回 false
 */
export function matchKey(key: string, include?: string[], exclude?: string[]): boolean {
  const isExcluded = exclude?.some((pattern) => simpleMatch(key, pattern));
  if (isExcluded) {
    return false;
  }

  if (include && include.length > 0) {
    return include.some((pattern) => simpleMatch(key, pattern));
  }

  return true;
}

/**
 * @description 用于模板字符串的简单变量替换
 * @param template 模板字符串，如 'https://foo.com/{name}/bar?ver={version}'
 * @param params 替换参数对象
 * @param ignoreUndefined 是否忽略未定义的变量，如果为 true，则保留原占位符，否则替换为 ''
 * @returns 替换后的字符串
 */
export function compileTemplate(
  template: string,
  params: Record<string, string | number | undefined>,
  ignoreUndefined = false,
): string {
  return template.replace(/\{(\w+)\}/g, (_, key) => {
    const value = params[key];
    if (value === undefined) {
      if (ignoreUndefined) return `{${key}}`;
      return '';
    }
    return encodeURIComponent(String(value));
  });
}

export const projectPaths = {
  workspace: ROOT_DIR, // 项目根目录
  packages: path.join(ROOT_DIR, 'packages'), // monorepo目录
  extensions: path.join(ROOT_DIR, 'packages', 'extensions'), // 插件矩阵目录
  shared: path.join(ROOT_DIR, 'packages', 'shared'), // 共享代码目录
  sharedLocales: path.join(ROOT_DIR, 'packages', 'shared', 'locales'), // 共享语言包目录
  output: path.join(ROOT_DIR, '.output', process.env.NODE_ENV === 'development' ? 'dist' : 'release'), // 构建产物目录
  release: path.join(ROOT_DIR, 'release'), // 发布环境下的构建产物目录
  scripts: path.join(ROOT_DIR, 'scripts'), // 脚本目录
};

/**
 * 校验目录是否存在，不存在则抛出错误
 * @param dirPath 绝对路径
 */
export function checkDirExists(dirPath: string) {
  if (!fs.existsSync(dirPath) || !fs.statSync(dirPath).isDirectory()) {
    throw new Error(`目录不存在: ${dirPath}`);
  }
}

/**
 * 确保目录存在，如果不存在则创建
 * @param dirPath 目录路径
 */
export function ensureDirExists(dirPath: string): void {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

/**
 * @description 安全地合并两个对象
 * @param target 目标对象
 * @param source 源对象
 * @param options 合并选项
 * @param options.deepMerge 是否深度合并对象，默认为 true
 * @param options.immutable 是否保留源对象不变，默认为 true
 * @param options.overrideArrays 是否覆盖数组，默认为 false（表示合并数组）
 * @returns 合并后的对象
 */
export function deepMerge<T extends Record<string, unknown>>(
  target: T,
  source: Partial<T>,
  options: DeepMergeOptions = {},
): T {
  const { deepMerge = true, immutable = true } = options;

  if (!source) {
    return immutable ? { ...target } : target;
  }

  if (deepMerge) {
    return immutable ? merge({}, target, source) : merge(target, source);
  } else {
    return immutable ? { ...target, ...source } : Object.assign(target, source);
  }
}

/**
 * 解析 variantTarget 字符串，提取其中的信息
 * @param variantTarget - 格式: `{webstore}-mv{manifestVersion}-{variantType}`
 * @returns 解析后的信息对象，如果格式不正确则返回 null
 */
export function parseVariantTarget(variantTarget: string): {
  webstore: string;
  manifestVersion: string;
  variantType: string;
} {
  // 匹配格式: webstore-mvX-variantType
  const match = variantTarget.match(/^(.+)-mv(\d+)-(.+)$/);

  if (!match) {
    return {
      webstore: '',
      manifestVersion: '',
      variantType: '',
    };
  }

  return {
    webstore: match[1] || '',
    manifestVersion: match[2] || '',
    variantType: match[3] || '',
  };
}
